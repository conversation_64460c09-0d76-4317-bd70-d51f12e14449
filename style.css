:root {
	--corner-radius: 10px;
	--progress-radius: 5px;
	--album-art-size: 100px;
	--album-art-margin: 0px 8px 0px 0px;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
}

#statusContainer {
	font-weight: 500;
	font-size: 30px;
	text-align: center;
	background-color: #D12025;
	color: white;
	padding: 10px;
	border-radius: var(--corner-radius);
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	opacity: 0;
}

#mainContainer {
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
	display: flex;
	position: absolute;
	height: var(--album-art-size);
	margin: 20px;
	filter: drop-shadow(15px 15px 7px rgba(0, 0, 0, 1));
	width: 100%;
	max-width: 500px;
	bottom: calc(50% - 20px);
	left: 50%;
	opacity: 0;
	transition: all 0.5s ease;
}

#albumArtBox {
	background: rgba(0, 0, 0, 0.5);
	position: relative;
	border-radius: var(--corner-radius);
	overflow: hidden;
	margin: var(--album-art-margin);
	object-fit: cover;
	width: var(--album-art-size);
}

#albumArt {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0%;
	object-fit: cover;
}
#albumArtBack {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

#songInfoBox {
	position: relative;
	color: white;
	width: calc(100% - 125px);
	display: flex;
	flex-direction: column;
	flex: 0 1 auto;
	justify-content: center;
	z-index: 1;
	text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
	overflow: hidden;
	z-index: 4;
}

#songInfo {
	background: rgba(0, 0, 0, 0.5);
	opacity: 0.9;
	position: relative;
	border-radius: var(--corner-radius);
	padding: 0px 20px;
	height: 100%;
	overflow: hidden;
}

#IAmRunningOutOfNamesForTheseBoxes {
	position: absolute;
	width: calc(100% - 40px);
	position: absolute;
	top: 50%;
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
}

#backgroundArt {
	position: absolute;
	height: 100%;
	width: 100%;
	border-radius: var(--corner-radius);
	overflow: hidden;
	z-index: -1;
	opacity: 0.9;
}

#backgroundImage {
	filter: blur(20px);
	position: absolute;
	width: 140%;
	top: 50%;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
}

#backgroundImageBack {
	filter: blur(20px);
	position: absolute;
	width: 140%;
	top: 50%;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
	z-index: -1;
}

#artistLabel {
	font-size: 16px;
	font-weight: 400;
	line-height: 1;
	white-space: nowrap;
	transition: all 0.5s ease;
}

#songLabel {
	font-weight: bold;
	font-size: 20px;	
	width: calc(100%);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	transition: all 0.5s ease;
}

#progressBg {
	margin-top: 15px;
	width: 100%;
	height: auto;
	border-radius: var(--progress-radius);
	background-color: #1F1F1F;
}

#progressBar {
	border-radius: var(--progress-radius);
	height: 5px;
	width: 20%;
	background-color: #ffffff;
	margin: 10px 0px;
	transition: all 1s ease;
}

#times {
	position: relative;
	height: 16px;
	font-size: 16px;
	font-weight: 700;
	line-height: 2.2;
}

#progressTime {
	position: absolute;
}

#timeRemaining {
	position: absolute;
	width: 100%;
	text-align: right;
}

.text-show {
	opacity: 1;
	transition: all 0.25s ease;
}

.text-fade {
	opacity: 0;
	transition: all 0.25s ease;
}
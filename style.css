:root {
	--corner-radius: 10px;
	--progress-radius: 5px;
	--album-art-size: 100px;
	--album-art-margin: 0px 8px 0px 0px;

	/* Full-screen layout responsive values */
	--fs-album-size-mobile: 20rem;
	--fs-album-size-desktop: 30rem;
	--fs-song-size-mobile: 2.5rem;
	--fs-song-size-desktop: 4.5rem;
	--fs-artist-size-mobile: 1.75rem;
	--fs-artist-size-desktop: 3rem;
	--fs-album-text-size-mobile: 1.25rem;
	--fs-album-text-size-desktop: 2rem;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
}

#statusContainer {
	font-weight: 500;
	font-size: 30px;
	text-align: center;
	background-color: #D12025;
	color: white;
	padding: 10px;
	border-radius: var(--corner-radius);
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	opacity: 0;
}

#mainContainer {
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
	display: flex;
	position: absolute;
	height: var(--album-art-size);
	margin: 20px;
	filter: drop-shadow(15px 15px 7px rgba(0, 0, 0, 1));
	width: 100%;
	max-width: 500px;
	bottom: calc(50% - 20px);
	left: 50%;
	opacity: 0;
	transition: all 0.5s ease;
}

#albumArtBox {
	background: rgba(0, 0, 0, 0.5);
	position: relative;
	border-radius: var(--corner-radius);
	overflow: hidden;
	margin: var(--album-art-margin);
	object-fit: cover;
	width: var(--album-art-size);
}

#albumArt {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0%;
	object-fit: cover;
}
#albumArtBack {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

#songInfoBox {
	position: relative;
	color: white;
	width: calc(100% - 125px);
	display: flex;
	flex-direction: column;
	flex: 0 1 auto;
	justify-content: center;
	z-index: 1;
	text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
	overflow: hidden;
	z-index: 4;
}

#songInfo {
	background: rgba(0, 0, 0, 0.5);
	opacity: 0.9;
	position: relative;
	border-radius: var(--corner-radius);
	padding: 0px 20px;
	height: 100%;
	overflow: hidden;
}

#IAmRunningOutOfNamesForTheseBoxes {
	position: absolute;
	width: calc(100% - 40px);
	position: absolute;
	top: 50%;
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
}

#backgroundArt {
	position: absolute;
	height: 100%;
	width: 100%;
	border-radius: var(--corner-radius);
	overflow: hidden;
	z-index: -1;
	opacity: 0.9;
}

#backgroundImage {
	filter: blur(20px);
	position: absolute;
	width: 140%;
	top: 50%;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
}

#backgroundImageBack {
	filter: blur(20px);
	position: absolute;
	width: 140%;
	top: 50%;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
	z-index: -1;
}

#artistLabel {
	font-size: 16px;
	font-weight: 400;
	line-height: 1;
	white-space: nowrap;
	transition: all 0.5s ease;
}

#songLabel {
	font-weight: bold;
	font-size: 20px;
	width: calc(100%);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	transition: all 0.5s ease;
}

#albumLabel {
	font-size: 14px;
	font-weight: 300;
	line-height: 1;
	white-space: nowrap;
	transition: all 0.5s ease;
	opacity: 0.8;
	display: none; /* Hidden by default, shown only in full-screen layout */
}

#progressBg {
	margin-top: 15px;
	width: 100%;
	height: auto;
	border-radius: var(--progress-radius);
	background-color: #1F1F1F;
}

#progressBar {
	border-radius: var(--progress-radius);
	height: 5px;
	width: 20%;
	background-color: #ffffff;
	margin: 10px 0px;
	transition: all 1s ease;
}

#times {
	position: relative;
	height: 16px;
	font-size: 16px;
	font-weight: 700;
	line-height: 2.2;
}

#progressTime {
	position: absolute;
}

#timeRemaining {
	position: absolute;
	width: 100%;
	text-align: right;
}

.text-show {
	opacity: 1;
	transition: all 0.25s ease;
}

.text-fade {
	opacity: 0;
	transition: all 0.25s ease;
}

/* ============================= */
/* FULL-SCREEN LAYOUT (STYLE=3) */
/* ============================= */

body.fullscreen-layout {
	margin: 0;
	padding: 0;
	overflow: hidden;
	background: #000;
}

/* Ensure all elements in full-screen layout use border-box sizing */
body.fullscreen-layout *,
body.fullscreen-layout *::before,
body.fullscreen-layout *::after {
	box-sizing: border-box;
}

body.fullscreen-layout #statusContainer {
	display: none;
}

body.fullscreen-layout #mainContainer {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	max-width: none;
	margin: 0;
	filter: none;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 0 3rem;
	gap: 3rem;
	opacity: 1;
	bottom: auto;
	transform: none;
	box-sizing: border-box; /* Include padding in width calculation */
}

/* Full-screen blurred background */
body.fullscreen-layout #mainContainer::before {
	content: '';
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	filter: blur(40px);
	z-index: -2;
	transform: scale(1.1);
}

/* Dark overlay over blurred background */
body.fullscreen-layout #mainContainer::after {
	content: '';
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.3);
	z-index: -1;
}

/* Full-screen album art */
body.fullscreen-layout #albumArtBox {
	width: var(--fs-album-size-mobile);
	height: var(--fs-album-size-mobile);
	background: transparent;
	border-radius: 1rem;
	margin: 0;
	flex-shrink: 0;
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.12),
				0 10px 20px rgba(0, 0, 0, 0.15),
				0 15px 28px rgba(0, 0, 0, 0.18),
				0 20px 38px rgba(0, 0, 0, 0.20);
}

/* Responsive padding for smaller screens */
@media (max-width: 768px) {
	body.fullscreen-layout #mainContainer {
		padding: 0 1.5rem;
		gap: 1.5rem;
	}
}

/* Desktop album art size */
@media (min-width: 1024px) {
	body.fullscreen-layout #albumArtBox {
		width: var(--fs-album-size-desktop);
		height: var(--fs-album-size-desktop);
	}
}

/* Full-screen song info */
body.fullscreen-layout #songInfoBox {
	width: auto;
	flex: 1;
	max-width: none;
	background: transparent;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
	min-width: 0; /* Prevent flex item from overflowing */
	overflow: hidden; /* Ensure content stays within bounds */
}

body.fullscreen-layout #songInfo {
	background: transparent;
	padding: 0;
	height: auto;
	opacity: 1;
}

body.fullscreen-layout #IAmRunningOutOfNamesForTheseBoxes {
	position: static;
	width: 100%;
	transform: none;
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

/* Full-screen typography */
body.fullscreen-layout #songLabel {
	font-size: var(--fs-song-size-mobile);
	font-weight: bold;
	color: white;
	line-height: 1.1;
	margin-bottom: 0.5rem;
}

body.fullscreen-layout #artistLabel {
	font-size: var(--fs-artist-size-mobile);
	font-weight: bold;
	color: white;
	line-height: 1.1;
	margin-bottom: 0.25rem;
}

body.fullscreen-layout #albumLabel {
	display: block;
	font-size: var(--fs-album-text-size-mobile);
	font-weight: 600;
	color: white;
	opacity: 0.8;
	line-height: 1.1;
	margin-bottom: 2rem;
}

/* Desktop typography */
@media (min-width: 1024px) {
	body.fullscreen-layout #songLabel {
		font-size: var(--fs-song-size-desktop);
	}

	body.fullscreen-layout #artistLabel {
		font-size: var(--fs-artist-size-desktop);
	}

	body.fullscreen-layout #albumLabel {
		font-size: var(--fs-album-text-size-desktop);
	}
}

/* Full-screen progress bar */
body.fullscreen-layout #progressBg {
	width: 100%;
	max-width: 100%; /* Ensure it doesn't exceed container width */
	height: 12px;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 6px;
	margin-top: 0;
	margin-bottom: 1rem;
	box-sizing: border-box; /* Include padding/border in width calculation */
}

body.fullscreen-layout #progressBar {
	height: 12px;
	background: white;
	border-radius: 6px;
	margin: 0;
	max-width: 100%; /* Ensure progress bar doesn't exceed container */
	box-sizing: border-box; /* Include padding/border in width calculation */
}

/* Full-screen time display */
body.fullscreen-layout #times {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 1.25rem;
	font-weight: 600;
	color: white;
	height: auto;
	line-height: 1;
}

body.fullscreen-layout #progressTime,
body.fullscreen-layout #timeRemaining {
	position: static;
	width: auto;
	text-align: left;
}

body.fullscreen-layout #timeRemaining {
	text-align: right;
}

/* Full-screen layout with hidden album art */
body.fullscreen-layout #albumArtBox[style*="display: none"] ~ #songInfoBox,
body.fullscreen-layout #songInfoBox:only-child {
	width: 100%;
	max-width: 60rem;
	margin: 0 auto;
	text-align: center;
}

/* Hide background elements in full-screen layout */
body.fullscreen-layout #backgroundArt {
	display: none;
}

/* Additional safety for very small screens */
@media (max-width: 480px) {
	body.fullscreen-layout #mainContainer {
		padding: 0 1rem;
		gap: 1rem;
		flex-direction: column;
		justify-content: center;
	}

	body.fullscreen-layout #albumArtBox {
		width: 15rem;
		height: 15rem;
	}

	body.fullscreen-layout #songLabel {
		font-size: 2rem;
	}

	body.fullscreen-layout #artistLabel {
		font-size: 1.5rem;
	}

	body.fullscreen-layout #albumLabel {
		font-size: 1rem;
	}
}
* {
	--corner-radius: 10px;
	--padding: 20px;
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
}

body {
	margin: 0px;
	color: white;
	font-size: 24px;
}

#mainContainer {
	position: absolute;
	width: 100vw;
	height: 100vh;
	margin: 0px;
	background-color: #282828;
}

#connectBox{
	background-color: #181818;
	border-radius: var(--corner-radius);
	padding: calc(var(--padding)*3) calc(var(--padding)*3);
	position: absolute;
	left: 50%;
	top: 50%;	
	transform: translateX(-50%) translateY(-50%);
	display: flex;
	filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 1));
	width: 400px;
	display: none;
}

#authorizationBox {
	background-color: #252525;
	border-radius: var(--corner-radius);
	padding: calc(var(--padding)*3) calc(var(--padding)*3);
	position: absolute;
	left: 50%;
	top: 50%;	
	transform: translateX(-50%) translateY(-50%);
	filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 1));
	width: 400px;
	display: none;
}

#authorizationComplete {
	display: block;
	text-align: center;
	padding-bottom: 40px;
}

button {
	font-size: 24px;
	font-weight: 500;
	background-color: #3be477;
	color: white;
	opacity: 0.8;
	border-width: 0;
	border-radius: var(--corner-radius);
	padding: calc(var(--padding)/2) var(--padding);
	width: 100%;
}

button:hover {
	opacity: 1;
	cursor: pointer;
}

button:disabled {
	opacity: 0.5;
	cursor: inherit;
}

#instructionsButton, #donateButton {
	background-color: #2e2e2e;	
}

#copyURLButton {
	background-color: #ffffff;
	color: #181818;
	width: 100%;
	transition: all 0.2s ease;
}

#copyURLButton:disabled {
	background-color: #636363;
}

input {
	border-radius: var(--corner-radius);
	width: calc(100% - 20px);
	font-size: 20px;
	margin: 10px 0px;
	padding: 10px 10px;
	background-color: #ffffff05;
	border-width: 0px;
	color: white;
}

textarea:focus, input:focus {
    outline: none;
}